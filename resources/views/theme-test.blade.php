@extends('layouts.admin')

@section('title', 'Theme Switcher Test')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-header">
                <h1 class="page-title">Theme Switcher Test</h1>
                <p class="page-subtitle">Testing the light/dark theme switching functionality</p>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Current Theme Information</h3>
                </div>
                <div class="card-body">
                    <div class="theme-info">
                        <p><strong>HTML data-theme-mode:</strong> <span id="current-theme"></span></p>
                        <p><strong>HTML data-header-styles:</strong> <span id="current-header"></span></p>
                        <p><strong>HTML data-menu-styles:</strong> <span id="current-menu"></span></p>
                        <p><strong>localStorage ynexdarktheme:</strong> <span id="local-dark"></span></p>
                        <p><strong>localStorage ynexHeader:</strong> <span id="local-header"></span></p>
                        <p><strong>localStorage ynexMenu:</strong> <span id="local-menu"></span></p>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Manual Theme Controls</h3>
                </div>
                <div class="card-body">
                    <div class="btn-group" role="group">
                        <button id="test-light" class="btn btn-primary">Switch to Light</button>
                        <button id="test-dark" class="btn btn-secondary">Switch to Dark</button>
                    </div>
                    <hr>
                    <p class="text-muted">Use the gear icon in the top-right corner to access the official theme switcher.</p>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Test Elements</h3>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="alert alert-primary" role="alert">
                                This is a primary alert—check it out!
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="alert alert-success" role="alert">
                                This is a success alert—check it out!
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="alert alert-warning" role="alert">
                                This is a warning alert—check it out!
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="test-input">Test Input</label>
                                <input type="text" class="form-control" id="test-input" placeholder="Enter some text">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="test-select">Test Select</label>
                                <select class="form-control" id="test-select">
                                    <option>Option 1</option>
                                    <option>Option 2</option>
                                    <option>Option 3</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Console Log</h3>
                </div>
                <div class="card-body">
                    <div id="console-log" style="background: var(--form-control-bg, #000); color: var(--custom-black, #0f0); padding: 15px; font-family: monospace; height: 200px; overflow-y: auto; border-radius: 4px; border: 1px solid var(--default-border, #ccc);"></div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Console logging function
    function log(message) {
        const logDiv = document.getElementById('console-log');
        const timestamp = new Date().toLocaleTimeString();
        logDiv.innerHTML += `[${timestamp}] ${message}<br>`;
        logDiv.scrollTop = logDiv.scrollHeight;
        console.log(message);
    }
    
    // Update theme info display
    function updateThemeInfo() {
        const html = document.querySelector('html');
        document.getElementById('current-theme').textContent = html.getAttribute('data-theme-mode') || 'not set';
        document.getElementById('current-header').textContent = html.getAttribute('data-header-styles') || 'not set';
        document.getElementById('current-menu').textContent = html.getAttribute('data-menu-styles') || 'not set';
        document.getElementById('local-dark').textContent = localStorage.getItem('ynexdarktheme') || 'not set';
        document.getElementById('local-header').textContent = localStorage.getItem('ynexHeader') || 'not set';
        document.getElementById('local-menu').textContent = localStorage.getItem('ynexMenu') || 'not set';
    }
    
    // Test functions
    function testLightTheme() {
        log('Testing light theme switch...');
        try {
            if (typeof lightFn === 'function') {
                lightFn();
                localStorage.setItem("ynexHeader", 'light');
                localStorage.removeItem("bodylightRGB");
                localStorage.removeItem("bodyBgRGB");
                localStorage.removeItem("ynexMenu");
                log('Light theme function executed successfully');
            } else {
                log('ERROR: lightFn function not found');
            }
        } catch (error) {
            log('ERROR in light theme: ' + error.message);
        }
        updateThemeInfo();
    }
    
    function testDarkTheme() {
        log('Testing dark theme switch...');
        try {
            if (typeof darkFn === 'function') {
                darkFn();
                localStorage.setItem("ynexMenu", 'dark');
                localStorage.setItem("ynexHeader", 'dark');
                log('Dark theme function executed successfully');
            } else {
                log('ERROR: darkFn function not found');
            }
        } catch (error) {
            log('ERROR in dark theme: ' + error.message);
        }
        updateThemeInfo();
    }
    
    log('Theme test page loaded');
    updateThemeInfo();
    
    // Test button event listeners
    document.getElementById('test-light').addEventListener('click', testLightTheme);
    document.getElementById('test-dark').addEventListener('click', testDarkTheme);
    
    // Check if switcher functions are available
    log('Checking available functions...');
    log('lightFn available: ' + (typeof lightFn !== 'undefined'));
    log('darkFn available: ' + (typeof darkFn !== 'undefined'));
    log('switcherClick available: ' + (typeof switcherClick !== 'undefined'));
    
    // Monitor theme changes
    const observer = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
            if (mutation.type === 'attributes' && 
                ['data-theme-mode', 'data-header-styles', 'data-menu-styles'].includes(mutation.attributeName)) {
                log('Attribute changed: ' + mutation.attributeName + ' = ' + mutation.target.getAttribute(mutation.attributeName));
                updateThemeInfo();
            }
        });
    });
    
    observer.observe(document.querySelector('html'), {
        attributes: true,
        attributeFilter: ['data-theme-mode', 'data-header-styles', 'data-menu-styles']
    });
    
    log('Theme test initialized successfully');
    
    // Test if the official switcher radio buttons work
    setTimeout(() => {
        const lightRadio = document.querySelector('#switcher-light-theme');
        const darkRadio = document.querySelector('#switcher-dark-theme');
        
        if (lightRadio && darkRadio) {
            log('Official theme switcher radio buttons found');
            
            lightRadio.addEventListener('change', function() {
                if (this.checked) {
                    log('Light theme radio button clicked');
                }
            });
            
            darkRadio.addEventListener('change', function() {
                if (this.checked) {
                    log('Dark theme radio button clicked');
                }
            });
        } else {
            log('WARNING: Official theme switcher radio buttons not found');
        }
    }, 1000);
});
</script>
@endpush
