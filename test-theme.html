<!DOCTYPE html>
<html lang="en" dir="ltr" data-nav-layout="vertical" data-theme-mode="light" data-header-styles="light" data-menu-styles="dark" data-toggled="close">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Theme Switcher Test</title>
    
    <!-- Bootstrap CSS -->
    <link id="style" href="public/assets/libs/bootstrap/css/bootstrap.min.css" rel="stylesheet">

    <!-- Main Styles -->
    <link href="public/assets/css/styles.css" rel="stylesheet">

    <!-- Icons -->
    <link href="public/assets/css/icons.css" rel="stylesheet">
    
    <style>
        body {
            padding: 20px;
            background: rgb(var(--body-bg-rgb2, 255, 255, 255));
            color: var(--default-text-color, #000);
        }
        .test-card {
            background: var(--custom-white, #fff);
            border: 1px solid var(--default-border, #dee2e6);
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .theme-info {
            margin: 10px 0;
            padding: 10px;
            background: var(--default-background, #f8f9fa);
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Theme Switcher Test</h1>
        
        <div class="test-card">
            <h3>Current Theme Information</h3>
            <div class="theme-info">
                <p><strong>HTML data-theme-mode:</strong> <span id="current-theme"></span></p>
                <p><strong>HTML data-header-styles:</strong> <span id="current-header"></span></p>
                <p><strong>HTML data-menu-styles:</strong> <span id="current-menu"></span></p>
                <p><strong>localStorage ynexdarktheme:</strong> <span id="local-dark"></span></p>
            </div>
        </div>
        
        <div class="test-card">
            <h3>Theme Controls</h3>
            <div class="row">
                <div class="col-md-6">
                    <button id="test-light" class="btn btn-primary me-2">Switch to Light</button>
                    <button id="test-dark" class="btn btn-secondary">Switch to Dark</button>
                </div>
            </div>
        </div>
        
        <div class="test-card">
            <h3>Official Theme Switcher</h3>
            <div class="row">
                <div class="col-4">
                    <div class="form-check">
                        <input class="form-check-input" type="radio" name="theme-style" id="switcher-light-theme" checked>
                        <label class="form-check-label" for="switcher-light-theme">Light</label>
                    </div>
                </div>
                <div class="col-4">
                    <div class="form-check">
                        <input class="form-check-input" type="radio" name="theme-style" id="switcher-dark-theme">
                        <label class="form-check-label" for="switcher-dark-theme">Dark</label>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="test-card">
            <h3>Console Log</h3>
            <div id="console-log" style="background: #000; color: #0f0; padding: 10px; font-family: monospace; height: 200px; overflow-y: auto;"></div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="public/assets/js/main.js"></script>
    <script src="public/assets/js/custom-switcher.min.js"></script>
    
    <script>
        // Console logging function
        function log(message) {
            const logDiv = document.getElementById('console-log');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.innerHTML += `[${timestamp}] ${message}<br>`;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(message);
        }
        
        // Update theme info display
        function updateThemeInfo() {
            const html = document.querySelector('html');
            document.getElementById('current-theme').textContent = html.getAttribute('data-theme-mode') || 'not set';
            document.getElementById('current-header').textContent = html.getAttribute('data-header-styles') || 'not set';
            document.getElementById('current-menu').textContent = html.getAttribute('data-menu-styles') || 'not set';
            document.getElementById('local-dark').textContent = localStorage.getItem('ynexdarktheme') || 'not set';
        }
        
        // Test functions
        function testLightTheme() {
            log('Testing light theme switch...');
            try {
                if (typeof lightFn === 'function') {
                    lightFn();
                    localStorage.setItem("ynexHeader", 'light');
                    localStorage.removeItem("bodylightRGB");
                    localStorage.removeItem("bodyBgRGB");
                    localStorage.removeItem("ynexMenu");
                    log('Light theme function executed successfully');
                } else {
                    log('ERROR: lightFn function not found');
                }
            } catch (error) {
                log('ERROR in light theme: ' + error.message);
            }
            updateThemeInfo();
        }
        
        function testDarkTheme() {
            log('Testing dark theme switch...');
            try {
                if (typeof darkFn === 'function') {
                    darkFn();
                    localStorage.setItem("ynexMenu", 'dark');
                    localStorage.setItem("ynexHeader", 'dark');
                    log('Dark theme function executed successfully');
                } else {
                    log('ERROR: darkFn function not found');
                }
            } catch (error) {
                log('ERROR in dark theme: ' + error.message);
            }
            updateThemeInfo();
        }
        
        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            log('Page loaded, initializing theme test...');
            updateThemeInfo();
            
            // Test button event listeners
            document.getElementById('test-light').addEventListener('click', testLightTheme);
            document.getElementById('test-dark').addEventListener('click', testDarkTheme);
            
            // Check if switcher functions are available
            log('Checking available functions...');
            log('lightFn available: ' + (typeof lightFn !== 'undefined'));
            log('darkFn available: ' + (typeof darkFn !== 'undefined'));
            log('switcherClick available: ' + (typeof switcherClick !== 'undefined'));
            
            // Monitor theme changes
            const observer = new MutationObserver(function(mutations) {
                mutations.forEach(function(mutation) {
                    if (mutation.type === 'attributes' && mutation.attributeName === 'data-theme-mode') {
                        log('Theme mode changed to: ' + mutation.target.getAttribute('data-theme-mode'));
                        updateThemeInfo();
                    }
                });
            });
            
            observer.observe(document.querySelector('html'), {
                attributes: true,
                attributeFilter: ['data-theme-mode', 'data-header-styles', 'data-menu-styles']
            });
            
            log('Theme test initialized successfully');
        });
    </script>
</body>
</html>
